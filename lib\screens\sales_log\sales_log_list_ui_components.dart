import 'package:flutter/material.dart';
import '../../models/sales_log.dart';
import '../../models/sales_log_display_item.dart';
import '../../models/transaction_type.dart';
import '../../models/category.dart' as model_category;
import '../../utils/currency_utils.dart';

import '../../widgets/confirmation_dialog.dart';
import '../../utils/app_colors.dart';
import '../../utils/payment_method_utils.dart';

/// 판매 기록 목록의 UI 컴포넌트들을 담당하는 클래스
///
/// 주요 기능:
/// - 개별 판매 기록 아이템 위젯
/// - 그룹 판매 기록 아이템 위젯
/// - 빈 상태 위젯
/// - 다이얼로그 위젯들
class SalesLogListUiComponents {
  /// 빈 상태 위젯
  static Widget buildEmptyState({
    required String selectedSeller,
    required TransactionType? selectedTransactionType,
  }) {
    String message = '판매 기록이 없습니다.';

    if (selectedSeller != '전체 판매자') {
      message = '$selectedSeller의 판매 기록이 없습니다.';
    }

    if (selectedTransactionType != null) {
      message = '${selectedTransactionType.displayName} 기록이 없습니다.';
    }

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.receipt_long_outlined, size: 64, color: Colors.grey),
          const SizedBox(height: 16),
          Text(
            message,
            style: const TextStyle(fontFamily: 'Pretendard', fontSize: 18, color: Colors.grey),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// 표시 아이템 위젯 (개별/그룹 구분)
  static Widget buildSalesLogItem(
    SalesLogDisplayItem displayItem, {
    required Function(SalesLog) onDelete,
    required Function(GroupedSale) onDeleteGroup,
    required Function(GroupedSale) onShowGroupDetail,
    Function(SalesLog)? onSingleTap,
    List<model_category.Category>? categories,
    Map<int, String>? productCategoryMap, // productId -> categoryName 매핑
  }) {
    if (displayItem is SingleItem) {
      return buildSingleSalesLogItem(
        displayItem.salesLog,
        onDelete: onDelete,
        onTap: onSingleTap,
        categories: categories,
        productCategoryMap: productCategoryMap,
      );
    } else if (displayItem is GroupedSale) {
      return buildGroupedSalesLogItem(
        displayItem,
        onDelete: onDeleteGroup,
        onShowDetail: onShowGroupDetail,
        categories: categories,
        productCategoryMap: productCategoryMap,
      );
    } else {
      return const SizedBox.shrink();
    }
  }

  /// 개별 판매 기록 아이템 위젯 (모던 카드 디자인)
  static Widget buildSingleSalesLogItem(
    SalesLog salesLog, {
    required Function(SalesLog) onDelete,
    Function(SalesLog)? onTap,
    List<model_category.Category>? categories,
    Map<int, String>? productCategoryMap, // productId -> categoryName 매핑
  }) {

    // 카테고리명과 상품명 조합 (저장된 카테고리명 우선 사용)
    String displayName = salesLog.productName;

    // 1. 저장된 카테고리명이 있으면 우선 사용
    if (salesLog.categoryName != null && salesLog.categoryName!.isNotEmpty) {
      displayName = '${salesLog.categoryName}-${salesLog.productName}';
    }
    // 2. 저장된 카테고리명이 없으면 현재 상품-카테고리 매핑에서 찾기 (하위 호환성)
    else if (productCategoryMap != null && salesLog.productId != null) {
      final categoryName = productCategoryMap[salesLog.productId];
      if (categoryName != null) {
        displayName = '$categoryName-${salesLog.productName}';
      }
    }

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      child: Card(
        elevation: 1,
        shadowColor: AppColors.elevation1,
        surfaceTintColor: AppColors.surfaceTint,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.zero,
        ),
        child: InkWell(
          onTap: onTap != null ? () => onTap(salesLog) : null,
          onLongPress: () => onDelete(salesLog),
          borderRadius: BorderRadius.zero,
          splashColor: AppColors.primarySeed.withValues(alpha: 0.08),
          highlightColor: AppColors.primarySeed.withValues(alpha: 0.04),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 12),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                // 상품 정보 (확장 가능)
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // 상품명과 아이콘들
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              displayName,
                              style: TextStyle(
                                fontFamily: 'Pretendard',
                                fontSize: 15,
                                fontWeight: FontWeight.w600,
                                color: AppColors.onSurface,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          const SizedBox(width: 4),
                          // 세트 할인 아이콘
                          if (salesLog.setDiscountAmount > 0)
                            Icon(
                              Icons.local_offer,
                              size: 16,
                              color: AppColors.success,
                            ),
                          // 수동 할인 아이콘
                          if (salesLog.manualDiscountAmount > 0)
                            Padding(
                              padding: const EdgeInsets.only(left: 2),
                              child: Icon(
                                Icons.money_off,
                                size: 16,
                                color: AppColors.error,
                              ),
                            ),
                        ],
                      ),

                      const SizedBox(height: 4),

                      // 날짜와 판매자 정보 (위치 바꿈)
                      Row(
                        children: [
                          Text(
                            _formatDateWithDayOfWeek(DateTime.fromMillisecondsSinceEpoch(salesLog.saleTimestamp)),
                            style: TextStyle(
                              fontFamily: 'Pretendard',
                              fontSize: 12,
                              color: AppColors.onSurfaceVariant,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Text(
                            '판매자: ${salesLog.sellerName ?? '알 수 없음'}',
                            style: TextStyle(
                              fontFamily: 'Pretendard',
                              fontSize: 13,
                              color: AppColors.onSurfaceVariant,
                            ),
                          ),
                          const Spacer(),
                          if (salesLog.paymentMethod != null && salesLog.paymentMethod!.isNotEmpty)
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 3),
                              decoration: BoxDecoration(
                                color: AppColors.surfaceVariant,
                                borderRadius: BorderRadius.circular(6),
                                border: Border.all(
                                  color: AppColors.neutral30.withValues(alpha: 0.5),
                                  width: 0.5,
                                ),
                              ),
                              child: Text(
                                PaymentMethodUtils.getDisplayName(salesLog.paymentMethod),
                                style: TextStyle(
                                  fontFamily: 'Pretendard',
                                  fontSize: 11,
                                  color: AppColors.onSurfaceVariant,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                        ],
                      ),
                    ],
                  ),
                ),

                const SizedBox(width: 12),

                // 우측 정보 (수량, 금액)
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    // 수량 정보
                    Text(
                      '${salesLog.soldQuantity}개',
                      style: TextStyle(
                        fontFamily: 'Pretendard',
                        fontSize: 13,
                        fontWeight: FontWeight.w600,
                        color: AppColors.onSurfaceVariant,
                      ),
                    ),

                    const SizedBox(height: 4),

                    // 총 금액 (할인 차감)
                    Text(
                      salesLog.transactionType == TransactionType.service
                        ? '서비스'
                        : CurrencyUtils.formatCurrency(salesLog.totalAmount - salesLog.setDiscountAmount - salesLog.manualDiscountAmount),
                      style: TextStyle(
                        fontFamily: 'Pretendard',
                        fontSize: 15,
                        fontWeight: FontWeight.bold,
                        color: salesLog.transactionType == TransactionType.service
                          ? AppColors.categoryElectricBlue
                          : AppColors.success,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 그룹 판매 기록 아이템 위젯 (모던 카드 디자인)
  static Widget buildGroupedSalesLogItem(
    GroupedSale groupedSale, {
    required Function(GroupedSale) onDelete,
    required Function(GroupedSale) onShowDetail,
    List<model_category.Category>? categories,
    Map<int, String>? productCategoryMap, // productId -> categoryName 매핑
  }) {

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      child: Card(
        elevation: 1,
        shadowColor: AppColors.elevation1,
        surfaceTintColor: AppColors.surfaceTint,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.zero,
        ),
        child: InkWell(
          onTap: () => onShowDetail(groupedSale),
          onLongPress: () => onDelete(groupedSale),
          borderRadius: BorderRadius.zero,
          splashColor: AppColors.primarySeed.withValues(alpha: 0.08),
          highlightColor: AppColors.primarySeed.withValues(alpha: 0.04),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 12),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                // 상품 정보 (확장 가능)
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // 그룹 제목과 아이콘들
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              _buildGroupTitleWithCategory(groupedSale, productCategoryMap),
                              style: TextStyle(
                                fontFamily: 'Pretendard',
                                fontSize: 15,
                                fontWeight: FontWeight.w600,
                                color: AppColors.onSurface,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          const SizedBox(width: 4),
                          // 폴더 아이콘 (다중 판매)
                          Icon(
                            Icons.folder_outlined,
                            size: 16,
                            color: AppColors.onSurfaceVariant,
                          ),
                          // 세트 할인 아이콘
                          if (groupedSale.hasSetDiscount)
                            Padding(
                              padding: const EdgeInsets.only(left: 2),
                              child: Icon(
                                Icons.local_offer,
                                size: 16,
                                color: AppColors.success,
                              ),
                            ),
                          // 수동 할인 아이콘
                          if (groupedSale.hasManualDiscount)
                            Padding(
                              padding: const EdgeInsets.only(left: 2),
                              child: Icon(
                                Icons.money_off,
                                size: 16,
                                color: AppColors.error,
                              ),
                            ),
                        ],
                      ),

                      const SizedBox(height: 4),

                      // 날짜와 판매자 정보 (위치 바꿈)
                      Row(
                        children: [
                          Text(
                            _formatDateWithDayOfWeek(DateTime.fromMillisecondsSinceEpoch(
                              groupedSale.representativeTimestampMillis(),
                            )),
                            style: TextStyle(
                              fontFamily: 'Pretendard',
                              fontSize: 12,
                              color: AppColors.onSurfaceVariant,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Text(
                            '판매자: ${groupedSale.sellerDisplayText}',
                            style: TextStyle(
                              fontFamily: 'Pretendard',
                              fontSize: 13,
                              color: AppColors.onSurfaceVariant,
                            ),
                          ),
                          const Spacer(),
                          // 그룹의 결제유형들 표시
                          if (_getGroupPaymentMethods(groupedSale).isNotEmpty)
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 3),
                              decoration: BoxDecoration(
                                color: AppColors.surfaceVariant,
                                borderRadius: BorderRadius.circular(6),
                                border: Border.all(
                                  color: AppColors.neutral30.withValues(alpha: 0.5),
                                  width: 0.5,
                                ),
                              ),
                              child: Text(
                                _getGroupPaymentMethods(groupedSale),
                                style: TextStyle(
                                  fontFamily: 'Pretendard',
                                  fontSize: 11,
                                  color: AppColors.onSurfaceVariant,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                        ],
                      ),
                    ],
                  ),
                ),

                const SizedBox(width: 16),

                // 우측 정보 (금액)
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    // 총 금액 (할인 차감)
                    Text(
                      CurrencyUtils.formatCurrency(groupedSale.representativeActualAmount()),
                      style: TextStyle(
                        fontFamily: 'Pretendard',
                        fontSize: 15,
                        fontWeight: FontWeight.bold,
                        color: AppColors.success,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }



  /// 삭제 확인 다이얼로그
  static Future<bool> showDeleteConfirmDialog(
    BuildContext context,
    SalesLog salesLog, {
    Map<int, String>? productCategoryMap,
  }) async {
    // 카테고리명-상품명 형식으로 표시명 생성 (저장된 카테고리명 우선 사용)
    String displayName = salesLog.productName;

    // 1. 저장된 카테고리명이 있으면 우선 사용
    if (salesLog.categoryName != null && salesLog.categoryName!.isNotEmpty) {
      displayName = '${salesLog.categoryName}-${salesLog.productName}';
    }
    // 2. 저장된 카테고리명이 없으면 현재 상품-카테고리 매핑에서 찾기 (하위 호환성)
    else if (productCategoryMap != null && salesLog.productId != null) {
      final categoryName = productCategoryMap[salesLog.productId];
      if (categoryName != null) {
        displayName = '$categoryName-${salesLog.productName}';
      }
    }

    return await ConfirmationDialog.showDelete(
      context: context,
      title: '판매 기록 삭제',
      message: '$displayName\n판매기록을 삭제하시겠습니까?\n삭제 시 재고가 복구됩니다.',
    ) ?? false;
  }

  /// 그룹 제목에 카테고리명을 포함하여 생성
  static String _buildGroupTitleWithCategory(
    GroupedSale groupedSale,
    Map<int, String>? productCategoryMap,
  ) {
    if (groupedSale.items.isEmpty) return '';

    // 첫 번째 아이템의 상품명에 카테고리명 추가
    final firstItem = groupedSale.items.first;
    String displayName = firstItem.productName;

    // 카테고리명 추가
    if (productCategoryMap != null && firstItem.productId != null) {
      final categoryName = productCategoryMap[firstItem.productId];
      if (categoryName != null) {
        displayName = '$categoryName-${firstItem.productName}';
      }
    }

    // 11글자 제한 적용 (카테고리명 포함)
    if (displayName.length <= 11) {
      // 그대로 사용
    } else {
      displayName = '${displayName.substring(0, 11)}..';
    }

    // 여러 종류인 경우 "외 N종류" 추가
    final itemCount = groupedSale.items.length;
    if (itemCount == 1) {
      return displayName;
    } else {
      return '$displayName 외 ${itemCount - 1}종류';
    }
  }

  /// 그룹 삭제 다이얼로그 설명 텍스트 생성 (간결한 버전)
  static String _buildDeleteGroupDescription(
    GroupedSale groupedSale,
    Map<int, String>? productCategoryMap,
  ) {
    final itemCount = groupedSale.items.length;

    // 첫 번째 상품의 카테고리명-상품명 형식 생성 (저장된 카테고리명 우선 사용)
    String displayName = groupedSale.representativeProductNameForDisplay;
    if (groupedSale.items.isNotEmpty) {
      final firstItem = groupedSale.items.first;

      // 1. 저장된 카테고리명이 있으면 우선 사용
      if (firstItem.categoryName != null && firstItem.categoryName!.isNotEmpty) {
        displayName = '${firstItem.categoryName}-${firstItem.productName}';
      }
      // 2. 저장된 카테고리명이 없으면 현재 상품-카테고리 매핑에서 찾기 (하위 호환성)
      else if (productCategoryMap != null && firstItem.productId != null) {
        final categoryName = productCategoryMap[firstItem.productId];
        if (categoryName != null) {
          displayName = '$categoryName-${firstItem.productName}';
        }
      }
    }

    if (itemCount == 1) {
      return '$displayName\n판매기록을 삭제하시겠습니까?\n삭제 시 재고가 복구됩니다.';
    } else {
      return '$displayName 외 ${itemCount - 1}종류\n다중 판매기록을 삭제하시겠습니까?\n삭제 시 재고가 복구됩니다.';
    }
  }

  /// 그룹 삭제 확인 다이얼로그
  static Future<bool> showDeleteGroupConfirmDialog(
    BuildContext context,
    GroupedSale groupedSale, {
    Map<int, String>? productCategoryMap,
  }) async {
    return await ConfirmationDialog.showDelete(
      context: context,
      title: '다중판매 기록 삭제',
      message: _buildDeleteGroupDescription(groupedSale, productCategoryMap),
      confirmLabel: '삭제',
      cancelLabel: '취소',
    ) ?? false;
  }

  /// 그룹 판매의 결제유형들을 문자열로 반환
  static String _getGroupPaymentMethods(GroupedSale groupedSale) {
    final paymentMethods = <String>{};
    for (final item in groupedSale.items) {
      if (item.paymentMethod != null && item.paymentMethod!.isNotEmpty) {
        paymentMethods.add(PaymentMethodUtils.getDisplayName(item.paymentMethod));
      }
    }

    if (paymentMethods.isEmpty) return '';

    final methods = paymentMethods.toList()..sort();
    if (methods.length == 1) {
      return methods.first;
    } else {
      return '${methods.first} 외 ${methods.length - 1}개';
    }
  }

  /// 날짜를 "N일(요일) N:N" 형태로 포맷팅
  static String _formatDateWithDayOfWeek(DateTime date) {
    const weekdays = ['월', '화', '수', '목', '금', '토', '일'];
    final weekday = weekdays[date.weekday - 1];
    return '${date.day}일($weekday) ${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
  }
}


