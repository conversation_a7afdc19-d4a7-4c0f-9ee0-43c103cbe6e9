
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:lucide_icons/lucide_icons.dart';
import 'package:intl/intl.dart';
import 'package:syncfusion_flutter_datepicker/datepicker.dart';
import 'package:dropdown_button2/dropdown_button2.dart';
import '../../utils/app_colors.dart';
import '../../utils/responsive_helper.dart';
import '../../utils/dimens.dart';
import '../../utils/logger_utils.dart';

import '../../providers/unified_workspace_provider.dart';
import '../../providers/seller_provider.dart';
import '../../providers/prepayment_provider.dart';
import '../../providers/checklist_provider.dart';
import '../../providers/home_dashboard_filter_provider.dart';
import '../../providers/revenue_goal_provider.dart';
import '../../utils/event_workspace_utils.dart';
import '../../widgets/event_image.dart';
import '../event/event_list_screen.dart';
import '../seller/seller_management_screen.dart';
import '../revenue_goal/revenue_goal_dialog.dart';

import '../../utils/subscription_utils.dart';
import '../../app/app_wrapper.dart';

/// 홈 대시보드 화면
/// 
/// 현재 행사 정보, 목표 수익 추적, 리스트, 판매 관리 등을 제공하는
/// 메인 대시보드 화면입니다.
class HomeDashboardScreen extends ConsumerWidget {
  const HomeDashboardScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentWorkspace = ref.watch(currentWorkspaceProvider);
    final currentEvent = currentWorkspace != null
        ? EventWorkspaceUtils.workspaceToEvent(currentWorkspace)
        : null;

    return AnnotatedRegion<SystemUiOverlayStyle>(
      value: SystemUiOverlayStyle(
        statusBarColor: AppColors.primarySeed,
        statusBarBrightness: Brightness.dark,
        statusBarIconBrightness: Brightness.light,
        systemNavigationBarColor: Colors.white,
        systemNavigationBarDividerColor: Colors.transparent,
        systemNavigationBarIconBrightness: Brightness.dark,
      ),
      child: Scaffold(
        body: SafeArea(
          child: currentEvent == null
              ? _buildNoEventState(context)
              : _buildDashboardContent(context, ref, currentEvent, currentWorkspace!),
        ),
      ),
    );
  }

  /// 행사가 선택되지 않은 상태
  Widget _buildNoEventState(BuildContext context) {
    return Center(
      child: Container(
        margin: ResponsiveHelper.getScreenPadding(context),
        padding: const EdgeInsets.all(Dimens.space24),
        decoration: BoxDecoration(
          color: AppColors.surface,
          borderRadius: BorderRadius.circular(Dimens.radiusL),
          boxShadow: [
            BoxShadow(
              color: AppColors.elevation2,
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              LucideIcons.calendar,
              size: 64,
              color: AppColors.primarySeed.withValues(alpha: 0.6),
            ),
            const SizedBox(height: Dimens.space16),
            Text(
              '행사를 선택해주세요',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: AppColors.onSurface,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: Dimens.space8),
            Text(
              '왼쪽 상단 메뉴에서 행사를 선택하거나\n새로운 행사를 생성해주세요',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppColors.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// 대시보드 메인 콘텐츠
  Widget _buildDashboardContent(BuildContext context, WidgetRef ref, dynamic currentEvent, dynamic currentWorkspace) {
    final sellerState = ref.watch(sellerNotifierProvider);
    final prepaymentState = ref.watch(prepaymentNotifierProvider);

    return SingleChildScrollView(
      padding: ResponsiveHelper.getScreenPadding(context),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 로고 영역
          _buildLogoSection(context),
          const SizedBox(height: Dimens.space2), // 16 -> 2로 줄임

          // 행사 정보 카드 (행사 이미지 포함)
          _buildEventInfoCard(context, currentEvent),
          const SizedBox(height: Dimens.space16),

          // 필터링 카드 (기간 + 판매자 선택)
          _buildFilterCard(context, sellerState),
          const SizedBox(height: Dimens.space16),

          // 목표 수익 추적 카드 (전체 너비)
          _buildRevenueTrackingCard(context),
          const SizedBox(height: Dimens.space16),

          // 새로운 레이아웃: 왼쪽 판매자, 오른쪽 나머지 카드들
          _buildMainCardsLayout(context, ref, sellerState, prepaymentState),

          // 하단 여백
          const SizedBox(height: Dimens.space24),
        ],
      ),
    );
  }

  /// 로고 섹션
  Widget _buildLogoSection(BuildContext context) {
    return Container(
      width: double.infinity,
      child: Center(
        child: Image.asset(
          'assets/images/bara_booth_manager_long.png',
          height: 80,
          fit: BoxFit.contain,
        ),
      ),
    );
  }

  /// 새로운 메인 카드 레이아웃 (반응형 배치)
  Widget _buildMainCardsLayout(BuildContext context, WidgetRef ref, dynamic sellerState, dynamic prepaymentState) {
    final screenWidth = MediaQuery.of(context).size.width;
    final orientation = MediaQuery.of(context).orientation;

    // 스마트폰 세로 모드 감지 (너비 600px 미만 + 세로 모드)
    final isPhonePortrait = screenWidth < 600 && orientation == Orientation.portrait;

    if (isPhonePortrait) {
      // 스마트폰 세로 모드: 판매관리가 전체 너비 사용
      return Column(
        children: [
          // 판매 관리 (전체 너비)
          _buildSalesManagementCard(context),
          const SizedBox(height: Dimens.space12),

          // 선입금 + 체크리스트 (2열)
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: _buildPrepaymentCard(context, prepaymentState),
              ),
              const SizedBox(width: Dimens.space12),
              Expanded(
                child: _buildChecklistCard(context),
              ),
            ],
          ),
          const SizedBox(height: Dimens.space12),

          // 판매자 관리 (전체 너비)
          _buildSellerManagementCard(context, ref, sellerState),
        ],
      );
    } else {
      // 태블릿 또는 가로 모드: 기존 2x2 레이아웃
      return Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 왼쪽: 판매 관리 + 선입금
          Expanded(
            flex: 1,
            child: Column(
              children: [
                _buildSalesManagementCard(context),
                const SizedBox(height: Dimens.space12),
                _buildPrepaymentCard(context, prepaymentState),
              ],
            ),
          ),
          const SizedBox(width: Dimens.space12),
          // 오른쪽: 체크리스트 + 판매자 관리
          Expanded(
            flex: 1,
            child: Column(
              children: [
                _buildChecklistCard(context),
                const SizedBox(height: Dimens.space12),
                _buildSellerManagementCard(context, ref, sellerState),
              ],
            ),
          ),
        ],
      );
    }
  }

  /// 행사 정보 카드 (행사 이미지 포함)
  Widget _buildEventInfoCard(BuildContext context, dynamic currentEvent) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () {
          // 행사 관리 페이지로 이동
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const EventListScreen(),
            ),
          );
        },
        borderRadius: BorderRadius.circular(Dimens.radiusL),
        child: Container(
          padding: const EdgeInsets.all(Dimens.space20),
          decoration: BoxDecoration(
            color: AppColors.surface,
            borderRadius: BorderRadius.circular(Dimens.radiusL),
            border: Border.all(
              color: AppColors.neutral60.withValues(alpha: 0.2),
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: AppColors.elevation1,
                blurRadius: 6,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            children: [
              // 왼쪽: 행사 이미지 (크기 증가)
              Container(
                width: 100,
                height: 100,
                decoration: BoxDecoration(
                  color: AppColors.primarySeed.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(Dimens.radiusM),
                  border: Border.all(
                    color: AppColors.primarySeed.withValues(alpha: 0.2),
                    width: 1,
                  ),
                ),
                child: EventImage(
                  imagePath: currentEvent?.imagePath,
                  width: 100,
                  height: 100,
                  fit: BoxFit.cover,
                  borderRadius: BorderRadius.circular(Dimens.radiusM),
                ),
              ),
              const SizedBox(width: Dimens.space16),
              // 오른쪽: 행사 정보
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            currentEvent?.name ?? '알 수 없음',
                            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: AppColors.onSurface,
                            ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        Icon(
                          LucideIcons.chevronRight,
                          color: AppColors.onSurfaceVariant,
                          size: 20,
                        ),
                      ],
                    ),
                    const SizedBox(height: Dimens.space12),
                    _buildInfoRow(context, '시작일', _formatDate(currentEvent?.startDate)),
                    const SizedBox(height: Dimens.space8),
                    _buildInfoRow(context, '종료일', _formatDate(currentEvent?.endDate)),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 필터링 카드 (기간 + 판매자 선택)
  Widget _buildFilterCard(BuildContext context, dynamic sellerState) {
    return Consumer(
      builder: (context, ref, child) {
        final dateRange = ref.watch(homeDashboardDateRangeProvider);
        final selectedSeller = ref.watch(homeDashboardSellerFilterProvider);

        // 판매자 목록 생성
        final sellers = <String>['전체'];
        if (sellerState?.sellers != null) {
          final sellerNames = (sellerState.sellers as List<dynamic>)
              .map((seller) => seller.name as String)
              .toList();
          sellers.addAll(sellerNames);
        }

        // 날짜 범위 표시 텍스트 생성
        String dateRangeText;
        if (dateRange == null) {
          dateRangeText = '전체 기간';
        } else {
          final startDate = dateRange.start;
          final endDate = dateRange.end;
          final startWeekday = _getWeekdayName(startDate.weekday);
          final endWeekday = _getWeekdayName(endDate.weekday);

          if (startDate.year == endDate.year &&
              startDate.month == endDate.month &&
              startDate.day == endDate.day) {
            // 같은 날짜인 경우
            dateRangeText = '${DateFormat('M월 d일').format(startDate)} ($startWeekday)';
          } else {
            // 다른 날짜인 경우
            dateRangeText = '${DateFormat('M월 d일').format(startDate)} ($startWeekday) ~ ${DateFormat('M월 d일').format(endDate)} ($endWeekday)';
          }
        }

        return Container(
          padding: const EdgeInsets.all(Dimens.space16),
          decoration: BoxDecoration(
            color: AppColors.surface,
            borderRadius: BorderRadius.circular(Dimens.radiusM),
            border: Border.all(
              color: AppColors.neutral60.withValues(alpha: 0.2),
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: AppColors.elevation1,
                blurRadius: 4,
                offset: const Offset(0, 1),
              ),
            ],
          ),
          child: Row(
            children: [
              // 필터 아이콘 (드롭다운 높이에 맞춰 크기 조정)
              Container(
                height: 40, // 드롭다운과 동일한 높이
                width: 40,
                decoration: BoxDecoration(
                  color: AppColors.primarySeed.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(Dimens.radiusXS),
                ),
                child: Icon(
                  LucideIcons.filter,
                  color: AppColors.primarySeed,
                  size: 24, // 크기 증가
                ),
              ),
              const SizedBox(width: Dimens.space12),
              // 기간 선택
              Expanded(
                child: _buildDateRangeSelector(context, ref, dateRangeText),
              ),
              const SizedBox(width: Dimens.space12),
              // 판매자 선택
              Expanded(
                child: _buildSellerDropdown(context, ref, selectedSeller, sellers),
              ),
            ],
          ),
        );
      },
    );
  }

  /// 요일 이름 반환 헬퍼 메서드
  String _getWeekdayName(int weekday) {
    switch (weekday) {
      case 1:
        return '월';
      case 2:
        return '화';
      case 3:
        return '수';
      case 4:
        return '목';
      case 5:
        return '금';
      case 6:
        return '토';
      case 7:
        return '일';
      default:
        return '';
    }
  }

  /// 날짜 범위 선택기 위젯
  Widget _buildDateRangeSelector(BuildContext context, WidgetRef ref, String dateRangeText) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '기간 선택',
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: AppColors.onSurfaceVariant,
            fontSize: 12,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 4),
        GestureDetector(
          onTap: () => _showDateRangePicker(context, ref),
          child: Container(
            height: 40,
            padding: const EdgeInsets.symmetric(horizontal: 12),
            decoration: BoxDecoration(
              color: AppColors.surface,
              border: Border.all(color: AppColors.neutral60.withValues(alpha: 0.3)),
              borderRadius: BorderRadius.circular(Dimens.radiusXS),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    dateRangeText,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: AppColors.onSurface,
                      fontSize: 13,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                Icon(
                  LucideIcons.calendar,
                  size: 16,
                  color: AppColors.onSurfaceVariant,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  /// 판매자 드롭다운 위젯
  Widget _buildSellerDropdown(BuildContext context, WidgetRef ref, String selectedSeller, List<String> sellers) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '판매자 선택',
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: AppColors.onSurfaceVariant,
            fontSize: 12,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 4),
        DropdownButtonHideUnderline(
          child: DropdownButton2<String>(
            value: selectedSeller,
            isExpanded: true,
            hint: Text(
              '판매자 선택',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppColors.onSurfaceVariant,
                fontSize: 13,
              ),
            ),
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppColors.onSurface,
              fontSize: 13,
            ),
            iconStyleData: IconStyleData(
              icon: Icon(
                LucideIcons.chevronDown,
                size: 16,
                color: AppColors.onSurfaceVariant,
              ),
            ),
            buttonStyleData: ButtonStyleData(
              height: 40,
              padding: const EdgeInsets.symmetric(horizontal: 12),
              decoration: BoxDecoration(
                color: AppColors.surface,
                border: Border.all(color: AppColors.neutral60.withValues(alpha: 0.3)),
                borderRadius: BorderRadius.circular(Dimens.radiusXS),
              ),
            ),
            dropdownStyleData: DropdownStyleData(
              maxHeight: 200,
              width: null, // 버튼과 같은 너비로 자동 설정
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 8,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
            ),
            menuItemStyleData: const MenuItemStyleData(
              height: 40,
              padding: EdgeInsets.symmetric(horizontal: 12),
            ),
            items: sellers.map((seller) {
              return DropdownMenuItem<String>(
                value: seller,
                child: Text(seller),
              );
            }).toList(),
            onChanged: (value) {
              if (value != null) {
                ref.read(homeDashboardSellerFilterProvider.notifier).state = value;
              }
            },
          ),
        ),
      ],
    );
  }

  /// 날짜 범위 선택 다이얼로그 표시
  void _showDateRangePicker(BuildContext context, WidgetRef ref) {
    final currentWorkspace = ref.read(currentWorkspaceProvider);

    // 매번 초기화 - 이전 선택 상태를 유지하지 않음
    PickerDateRange? initialRange;

    // 행사 날짜 범위 설정 (행사가 없으면 기본값 사용)
    DateTime minDate = currentWorkspace?.startDate ?? DateTime.now().subtract(const Duration(days: 365));
    DateTime maxDate = currentWorkspace?.endDate ?? DateTime.now().add(const Duration(days: 365));

    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          backgroundColor: AppColors.surface,
          surfaceTintColor: Colors.transparent,
          title: Text(
            '기간 선택',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: AppColors.onSurface,
              fontWeight: FontWeight.w600,
            ),
          ),
          content: SizedBox(
            width: 300,
            height: 350,
            child: SfDateRangePicker(
              selectionMode: DateRangePickerSelectionMode.range,
              initialSelectedRange: initialRange,
              minDate: minDate,
              maxDate: maxDate,
              backgroundColor: AppColors.surface,
              todayHighlightColor: AppColors.primarySeed,
              selectionColor: AppColors.primarySeed,
              startRangeSelectionColor: AppColors.primarySeed,
              endRangeSelectionColor: AppColors.primarySeed,
              rangeSelectionColor: AppColors.primarySeed.withValues(alpha: 0.3),
              selectionTextStyle: TextStyle(
                color: AppColors.onPrimary,
                fontWeight: FontWeight.w500,
              ),
              rangeTextStyle: TextStyle(
                color: AppColors.onSurface,
                fontWeight: FontWeight.w400,
              ),
              headerStyle: DateRangePickerHeaderStyle(
                backgroundColor: AppColors.surface,
                textStyle: TextStyle(
                  color: AppColors.onSurface,
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              monthViewSettings: DateRangePickerMonthViewSettings(
                firstDayOfWeek: 1, // 월요일부터 시작
                dayFormat: 'EEE',
                viewHeaderStyle: DateRangePickerViewHeaderStyle(
                  backgroundColor: AppColors.surface,
                  textStyle: TextStyle(
                    color: AppColors.onSurfaceVariant,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              monthCellStyle: DateRangePickerMonthCellStyle(
                textStyle: TextStyle(
                  color: AppColors.onSurface,
                  fontSize: 14,
                ),
                todayTextStyle: TextStyle(
                  color: AppColors.primarySeed,
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
                disabledDatesTextStyle: TextStyle(
                  color: AppColors.onSurface.withValues(alpha: 0.4),
                  fontSize: 14,
                ),
                leadingDatesTextStyle: TextStyle(
                  color: AppColors.onSurface.withValues(alpha: 0.6),
                  fontSize: 14,
                ),
                trailingDatesTextStyle: TextStyle(
                  color: AppColors.onSurface.withValues(alpha: 0.6),
                  fontSize: 14,
                ),
              ),
              onSelectionChanged: (DateRangePickerSelectionChangedArgs args) {
                if (args.value is PickerDateRange) {
                  final range = args.value as PickerDateRange;
                  if (range.startDate != null) {
                    // endDate가 null이면 startDate와 같은 날짜로 설정 (단일 날짜 선택)
                    final endDate = range.endDate ?? range.startDate!;
                    ref.read(homeDashboardDateRangeProvider.notifier).state =
                        DateTimeRange(start: range.startDate!, end: endDate);
                  }
                }
              },
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                ref.read(homeDashboardDateRangeProvider.notifier).state = null;
                Navigator.of(context).pop();
              },
              style: TextButton.styleFrom(
                foregroundColor: AppColors.onSurfaceVariant,
              ),
              child: const Text('전체 기간'),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              style: TextButton.styleFrom(
                foregroundColor: AppColors.primarySeed,
              ),
              child: const Text('확인'),
            ),
          ],
        );
      },
    );
  }



  /// 목표 수익 추적 카드
  Widget _buildRevenueTrackingCard(BuildContext context) {
    return Consumer(
      builder: (context, ref, child) {
        final revenueGoalStats = ref.watch(revenueGoalStatsProvider);
        final formatter = NumberFormat('#,###');

        final targetText = revenueGoalStats.totalTarget > 0
            ? '₩ ${formatter.format(revenueGoalStats.totalTarget)}'
            : '목표 없음';
        final actualText = '₩ ${formatter.format(revenueGoalStats.totalActual)}';
        final progress = revenueGoalStats.achievementRate.clamp(0.0, 1.0);
        final displayProgress = revenueGoalStats.achievementRate; // 100% 초과도 표시

        return _buildDashboardCard(
          context: context,
          icon: LucideIcons.trendingUp,
          title: '목표 수익 추적',
          subtitle: '달성률 추적',
          onTap: () async {
            await showDialog(
              context: context,
              builder: (context) => const RevenueGoalDialog(),
            );

            // 다이얼로그 닫힌 후 목표 수익 데이터 갱신
            LoggerUtils.logDebug('목표 수익 다이얼로그 닫힌 후 데이터 갱신 시작', tag: 'HomeDashboard');
            ref.invalidate(revenueGoalStatsProvider);
            ref.invalidate(revenueGoalNotifierProvider);
            ref.invalidate(currentWorkspaceProvider);

            // 데이터 다시 로드 (비동기로 실행하여 UI 블로킹 방지)
            Future.microtask(() async {
              try {
                await ref.read(revenueGoalNotifierProvider.notifier).loadGoals(showLoading: false);
                LoggerUtils.logDebug('목표 수익 데이터 갱신 완료', tag: 'HomeDashboard');
              } catch (e) {
                LoggerUtils.logError('목표 수익 데이터 갱신 실패', tag: 'HomeDashboard', error: e);
              }
            });
          },
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: Dimens.space12),
              _buildInfoRow(context, '목표 수익', targetText),
              const SizedBox(height: Dimens.space8),
              _buildInfoRow(context, '현재 수익', actualText),
              const SizedBox(height: Dimens.space8),
              _buildProgressBar(context, progress, displayProgress),
            ],
          ),
        );
      },
    );
  }

  /// 리스트 카드 (진행률 바 포함)
  Widget _buildChecklistCard(BuildContext context) {
    return Consumer(
      builder: (context, ref, child) {
        final checklistState = ref.watch(checklistNotifierProvider);
        final currentWorkspace = ref.watch(currentWorkspaceProvider);

        // 앱 시작 시 리스트 데이터 자동 로드 (한 번만 실행)
        if (!checklistState.isLoading && checklistState.templates.isEmpty && checklistState.error == null) {
          // 무한 재빌드 방지를 위해 Future.microtask 사용
          Future.microtask(() {
            if (checklistState.templates.isEmpty) {
              ref.read(checklistNotifierProvider.notifier).loadData(showLoading: false);
            }
          });
        }

        // 진행률 계산 (현재 워크스페이스 기준)
        int completedCount = 0;
        if (currentWorkspace != null) {
          for (final template in checklistState.templates) {
            if (checklistState.getCheckState(template.id!, currentWorkspace.id)) {
              completedCount++;
            }
          }
        }
        final totalCount = checklistState.templates.length;
        final progress = totalCount > 0 ? completedCount / totalCount : 0.0;

    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () {
          print('체크리스트 카드 클릭됨 - 네비게이션 시작');
          try {
            Navigator.of(context).pushNamed('/checklist');
            print('체크리스트 네비게이션 성공');
          } catch (e) {
            print('체크리스트 네비게이션 에러: $e');
          }
        },
        borderRadius: BorderRadius.circular(Dimens.radiusM),
        child: Container(
          padding: const EdgeInsets.all(Dimens.space16),
          decoration: BoxDecoration(
            color: AppColors.surface,
            borderRadius: BorderRadius.circular(Dimens.radiusM),
            border: Border.all(
              color: AppColors.neutral60.withValues(alpha: 0.2),
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: AppColors.elevation1,
                blurRadius: 4,
                offset: const Offset(0, 1),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(Dimens.space6),
                    decoration: BoxDecoration(
                      color: AppColors.primarySeed.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(Dimens.radiusXS),
                    ),
                    child: Icon(
                      LucideIcons.checkSquare,
                      color: AppColors.primarySeed,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: Dimens.space8),
                  Expanded(
                    child: Text(
                      'TODO 리스트',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppColors.onSurfaceVariant,
                        fontSize: 13,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  Icon(
                    LucideIcons.chevronRight,
                    color: AppColors.onSurfaceVariant,
                    size: 16,
                  ),
                ],
              ),
              const SizedBox(height: Dimens.space12),
              if (checklistState.isLoading)
                const Text(
                  '로딩 중...',
                  style: TextStyle(
                    color: AppColors.onSurfaceVariant,
                    fontSize: 16,
                  ),
                )
              else ...[
                Text(
                  '$completedCount/$totalCount',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.onSurface,
                    fontSize: 18,
                  ),
                ),
                const SizedBox(height: Dimens.space4),
                Text(
                  '완료됨',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppColors.onSurfaceVariant,
                    fontSize: 12,
                  ),
                ),
                const SizedBox(height: Dimens.space8),
                LinearProgressIndicator(
                  value: progress,
                  backgroundColor: AppColors.neutral60.withValues(alpha: 0.2),
                  valueColor: AlwaysStoppedAnimation<Color>(AppColors.primarySeed),
                  minHeight: 4,
                  borderRadius: BorderRadius.circular(2),
                ),
              ],
            ],
          ),
        ),
      ),
    );
      },
    );
  }



  /// 판매 관리 카드 (실제 데이터 연결)
  Widget _buildSalesManagementCard(BuildContext context) {
    return Consumer(
      builder: (context, ref, child) {
        // 필터링된 판매 통계 사용
        final salesStats = ref.watch(homeDashboardSalesStatsProvider);

        // 전체 판매 통계 (필터링 적용)
        final totalSales = salesStats['totalSales'] ?? 0;
        final salesCount = salesStats['salesCount'] ?? 0;
        final productCount = salesStats['productCount'] ?? 0;
        final serviceCount = salesStats['serviceCount'] ?? 0;
        final setDiscountCount = salesStats['setDiscountCount'] ?? 0;
        final totalDiscountAmount = salesStats['totalDiscountAmount'] ?? 0;



        return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () {
          // 기록&통계 탭으로 이동 (하단 바 유지)
          AppWrapper.changeTabIndex(3);
        },
        borderRadius: BorderRadius.circular(Dimens.radiusM),
        child: Container(
          padding: const EdgeInsets.all(Dimens.space16),
          decoration: BoxDecoration(
            color: AppColors.surface,
            borderRadius: BorderRadius.circular(Dimens.radiusM),
            border: Border.all(
              color: AppColors.neutral60.withValues(alpha: 0.2),
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: AppColors.elevation1,
                blurRadius: 4,
                offset: const Offset(0, 1),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(Dimens.space6),
                    decoration: BoxDecoration(
                      color: AppColors.primarySeed.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(Dimens.radiusXS),
                    ),
                    child: Icon(
                      LucideIcons.shoppingCart,
                      color: AppColors.primarySeed,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: Dimens.space8),
                  Expanded(
                    child: Text(
                      '판매 통계',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppColors.onSurfaceVariant,
                        fontSize: 13,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  Icon(
                    LucideIcons.chevronRight,
                    color: AppColors.onSurfaceVariant,
                    size: 16,
                  ),
                ],
              ),
              const SizedBox(height: Dimens.space12),
              Text(
                '${NumberFormat('#,###').format(totalSales)}원',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppColors.onSurface,
                  fontSize: 18,
                ),
              ),
              const SizedBox(height: Dimens.space2),
              Text(
                '총 매출',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: AppColors.onSurfaceVariant,
                  fontSize: 12,
                ),
              ),
              const SizedBox(height: Dimens.space12),
              // 2x3 그리드 레이아웃 (새로운 통계 항목들)
              Column(
                children: [
                  // 첫 번째 행
                  Row(
                    children: [
                      Expanded(
                        child: _buildSalesInfoItem(
                          context,
                          '$salesCount건',
                          '거래 횟수',
                          AppColors.onSurface,
                        ),
                      ),
                      const SizedBox(width: Dimens.space8),
                      Expanded(
                        child: _buildSalesInfoItem(
                          context,
                          '$productCount개',
                          '판매 개수',
                          AppColors.onSurface,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: Dimens.space8),
                  // 두 번째 행
                  Row(
                    children: [
                      Expanded(
                        child: _buildSalesInfoItem(
                          context,
                          '$serviceCount개',
                          '서비스 개수',
                          AppColors.onSurface,
                        ),
                      ),
                      const SizedBox(width: Dimens.space8),
                      Expanded(
                        child: _buildSalesInfoItem(
                          context,
                          '$setDiscountCount건',
                          '세트 판매 건수',
                          AppColors.onSurface,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: Dimens.space8),
                  // 세 번째 행 (총 할인액만 표시)
                  Row(
                    children: [
                      Expanded(
                        child: _buildSalesInfoItem(
                          context,
                          '${NumberFormat('#,###').format(totalDiscountAmount)}원',
                          '총 할인액',
                          AppColors.onSurface,
                        ),
                      ),
                      const SizedBox(width: Dimens.space8),
                      const Expanded(child: SizedBox()), // 빈 공간
                    ],
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
      },
    );
  }

  /// 판매 정보 아이템 위젯
  Widget _buildSalesInfoItem(BuildContext context, String value, String label, Color valueColor) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          value,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: valueColor,
            fontSize: 14,
            fontWeight: FontWeight.w600,
          ),
        ),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: AppColors.onSurfaceVariant,
            fontSize: 11,
          ),
        ),
      ],
    );
  }

  /// 판매자 관리 카드 (로컬 데이터 직접 사용)
  Widget _buildSellerManagementCard(BuildContext context, WidgetRef ref, SellerState sellerState) {
    // 로컬 데이터 직접 사용 (로딩 없음)
    final sellers = sellerState.sellers;
    final sellerNames = sellers.map((seller) => seller.name).toList();

    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () async {
          // 판매자 관리 기능 접근 권한 확인
          final hasAccess = await SubscriptionUtils.checkFeatureAccess(
            ref: ref,
            context: context,
            featureName: 'sellerManagement',
          );

          if (hasAccess) {
            // 🔥 판매자 관리 페이지로 이동하기 전 Provider 새로고침 (최신 구독 상태 반영)
            ref.invalidate(sellerNotifierProvider);

            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const SellerManagementScreen(),
              ),
            );
          }
        },
        borderRadius: BorderRadius.circular(Dimens.radiusM),
        child: Container(
          height: 280, // 고정 높이
          padding: const EdgeInsets.all(Dimens.space16),
          decoration: BoxDecoration(
            color: AppColors.surface,
            borderRadius: BorderRadius.circular(Dimens.radiusM),
            border: Border.all(
              color: AppColors.neutral60.withValues(alpha: 0.2),
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: AppColors.elevation1,
                blurRadius: 4,
                offset: const Offset(0, 1),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 헤더
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(Dimens.space6),
                    decoration: BoxDecoration(
                      color: AppColors.primarySeed.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(Dimens.radiusXS),
                    ),
                    child: Icon(
                      LucideIcons.users,
                      color: AppColors.primarySeed,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: Dimens.space8),
                  Text(
                    '판매자',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppColors.onSurfaceVariant,
                      fontSize: 13,
                    ),
                  ),
                  const SizedBox(width: Dimens.space4),
                  Text(
                    '${sellerNames.length}명',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppColors.primarySeed,
                      fontSize: 13,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  Icon(
                    LucideIcons.chevronRight,
                    color: AppColors.onSurfaceVariant,
                    size: 16,
                  ),
                ],
              ),
              const SizedBox(height: Dimens.space12),

              // 스크롤 가능한 판매자 리스트
              Expanded(
                child: SingleChildScrollView(
                        child: Column(
                          children: sellerNames.map((name) => Container(
                            margin: const EdgeInsets.only(bottom: Dimens.space8),
                            padding: const EdgeInsets.symmetric(
                              horizontal: Dimens.space12,
                              vertical: Dimens.space8,
                            ),
                            decoration: BoxDecoration(
                              color: AppColors.surfaceVariant.withValues(alpha: 0.3),
                              borderRadius: BorderRadius.circular(Dimens.radiusS),
                              border: Border.all(
                                color: AppColors.neutral60.withValues(alpha: 0.2),
                                width: 1,
                              ),
                            ),
                            child: Row(
                              children: [
                                Container(
                                  width: 24,
                                  height: 24,
                                  decoration: BoxDecoration(
                                    color: AppColors.primarySeed.withValues(alpha: 0.2),
                                    shape: BoxShape.circle,
                                  ),
                                  child: Icon(
                                    LucideIcons.user,
                                    color: AppColors.primarySeed,
                                    size: 12,
                                  ),
                                ),
                                const SizedBox(width: Dimens.space8),
                                Expanded(
                                  child: Text(
                                    name,
                                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                      color: AppColors.onSurface,
                                      fontSize: 14,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          )).toList(),
                        ),
                      ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 선입금 카드 (실제 데이터 연결)
  Widget _buildPrepaymentCard(BuildContext context, dynamic prepaymentState) {
    return Consumer(
      builder: (context, ref, child) {
        // 필터링된 선입금 데이터 사용
        final filteredPrepayments = ref.watch(homeDashboardFilteredPrepaymentsProvider);
        final selectedDay = ref.watch(homeDashboardPrepaymentDayFilterProvider);
        final availableDays = ref.watch(homeDashboardAvailableDaysProvider);

        // 실제 데이터 계산
        int receivedCount = 0;
        int totalCount = filteredPrepayments.length;

        for (final prepayment in filteredPrepayments) {
          if (prepayment.isReceived == true) {
            receivedCount++;
          }
        }

        final percentage = totalCount > 0 ? (receivedCount / totalCount * 100).round() : 0;
        final progress = totalCount > 0 ? receivedCount / totalCount : 0.0;
        final remainingCount = totalCount - receivedCount;

        return Container(
          padding: const EdgeInsets.all(Dimens.space16),
          decoration: BoxDecoration(
            color: AppColors.surface,
            borderRadius: BorderRadius.circular(Dimens.radiusM),
            border: Border.all(
              color: AppColors.neutral60.withValues(alpha: 0.2),
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: AppColors.elevation1,
                blurRadius: 4,
                offset: const Offset(0, 1),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(Dimens.space6),
                    decoration: BoxDecoration(
                      color: AppColors.primarySeed.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(Dimens.radiusXS),
                    ),
                    child: Icon(
                      LucideIcons.creditCard,
                      color: AppColors.primarySeed,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: Dimens.space8),
                  Expanded(
                    child: Text(
                      '선입금',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppColors.onSurfaceVariant,
                        fontSize: 13,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  // 요일 선택 드롭다운 (필터링 기능)
                  _buildPrepaymentDayDropdown(context, ref, selectedDay, availableDays),
                ],
              ),
              const SizedBox(height: Dimens.space12),
              Text(
                '$receivedCount/$totalCount',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppColors.onSurface,
                  fontSize: 18,
                ),
              ),
              const SizedBox(height: Dimens.space2),
              Text(
                '$percentage% 수령',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: AppColors.onSurfaceVariant,
                  fontSize: 12,
                ),
              ),
              const SizedBox(height: Dimens.space8),
              LinearProgressIndicator(
                value: progress,
                backgroundColor: AppColors.neutral60.withValues(alpha: 0.2),
                valueColor: AlwaysStoppedAnimation<Color>(AppColors.primarySeed),
                minHeight: 4,
                borderRadius: BorderRadius.circular(2),
              ),
              const SizedBox(height: Dimens.space8),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    '수령: $receivedCount건',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppColors.primarySeed,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  Text(
                    '미수령: $remainingCount건',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppColors.onSurfaceVariant,
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }





  /// 공통 대시보드 카드 위젯
  Widget _buildDashboardCard({
    required BuildContext context,
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
    Widget? child,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(Dimens.radiusL),
        child: Container(
          padding: const EdgeInsets.all(Dimens.space20),
          decoration: BoxDecoration(
            color: AppColors.surface,
            borderRadius: BorderRadius.circular(Dimens.radiusL),
            border: Border.all(
              color: AppColors.neutral60.withValues(alpha: 0.2),
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: AppColors.elevation1,
                blurRadius: 6,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(Dimens.space8),
                    decoration: BoxDecoration(
                      color: AppColors.primarySeed.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(Dimens.radiusS),
                    ),
                    child: Icon(
                      icon,
                      color: AppColors.primarySeed,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: Dimens.space12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          title,
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: AppColors.onSurface,
                            fontSize: 16,
                          ),
                        ),
                        Text(
                          subtitle,
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: AppColors.onSurfaceVariant,
                            fontSize: 13,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Icon(
                    LucideIcons.chevronRight,
                    color: AppColors.onSurfaceVariant,
                    size: 20,
                  ),
                ],
              ),
              if (child != null) child,
            ],
          ),
        ),
      ),
    );
  }

  /// 정보 행 위젯
  Widget _buildInfoRow(BuildContext context, String label, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: AppColors.onSurfaceVariant,
            fontSize: 13,
          ),
        ),
        Text(
          value,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: AppColors.onSurface,
            fontWeight: FontWeight.w500,
            fontSize: 14,
          ),
        ),
      ],
    );
  }

  /// 진행률 바 위젯
  Widget _buildProgressBar(BuildContext context, double progress, [double? displayProgress]) {
    final actualDisplayProgress = displayProgress ?? progress;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              '진행률',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: AppColors.onSurfaceVariant,
                fontSize: 13,
              ),
            ),
            Text(
              '${(actualDisplayProgress * 100).toInt()}%',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: actualDisplayProgress > 1.0 ? AppColors.success : AppColors.primarySeed,
                fontWeight: FontWeight.bold,
                fontSize: 14,
              ),
            ),
          ],
        ),
        const SizedBox(height: Dimens.space4),
        LinearProgressIndicator(
          value: progress,
          backgroundColor: AppColors.neutral60.withValues(alpha: 0.2),
          valueColor: AlwaysStoppedAnimation<Color>(AppColors.primarySeed),
          borderRadius: BorderRadius.circular(Dimens.radiusXS),
        ),
      ],
    );
  }

  /// 날짜 포맷팅
  String _formatDate(DateTime? date) {
    if (date == null) return '알 수 없음';
    return '${date.year}.${date.month.toString().padLeft(2, '0')}.${date.day.toString().padLeft(2, '0')}';
  }

  /// 선입금 요일 드롭다운 위젯
  Widget _buildPrepaymentDayDropdown(BuildContext context, WidgetRef ref, int selectedDay, Set<int> availableDays) {
    return Container(
      height: 28,
      padding: const EdgeInsets.symmetric(horizontal: 8),
      decoration: BoxDecoration(
        color: AppColors.surface,
        border: Border.all(color: AppColors.neutral60.withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(Dimens.radiusXS),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton2<int>(
          value: selectedDay,
          isExpanded: false,
          hint: Text(
            '요일 선택',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: AppColors.onSurfaceVariant,
              fontSize: 11,
            ),
          ),
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: AppColors.onSurface,
            fontSize: 11,
          ),
          iconStyleData: IconStyleData(
            icon: Icon(
              LucideIcons.chevronDown,
              size: 12,
              color: AppColors.onSurfaceVariant,
            ),
          ),
          buttonStyleData: const ButtonStyleData(
            padding: EdgeInsets.zero,
          ),
          dropdownStyleData: DropdownStyleData(
            maxHeight: 200,
            width: 120, // 적절한 너비 설정
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 8,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
          ),
          menuItemStyleData: const MenuItemStyleData(
            height: 32,
            padding: EdgeInsets.symmetric(horizontal: 12),
          ),
          items: () {
            final List<DropdownMenuItem<int>> items = [
              DropdownMenuItem<int>(
                value: 0,
                child: Text(
                  '전체',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppColors.onSurface,
                    fontSize: 11,
                  ),
                ),
              ),
            ];

            // 요일을 올바른 순서로 정렬: 월(1) → 화(2) → ... → 일(7) → 없음(8)
            final sortedDays = availableDays.toList()..sort((a, b) {
              // 8(없음)은 맨 마지막으로
              if (a == 8 && b != 8) return 1;
              if (b == 8 && a != 8) return -1;
              return a.compareTo(b);
            });

            for (final day in sortedDays) {
              String dayName;
              if (day == 8) {
                dayName = '없음';
              } else {
                dayName = _getDayOfWeekFullName(day);
              }
              items.add(
                DropdownMenuItem<int>(
                  value: day,
                  child: Text(
                    dayName,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppColors.onSurface,
                      fontSize: 11,
                    ),
                  ),
                ),
              );
            }

            return items;
          }(),
          onChanged: (value) {
            if (value != null) {
              ref.read(homeDashboardPrepaymentDayFilterProvider.notifier).state = value;
            }
          },
        ),
      ),
    );
  }

  /// 요일 전체 이름 반환 헬퍼 메서드
  String _getDayOfWeekFullName(int dayOfWeek) {
    switch (dayOfWeek) {
      case 1:
        return '월요일';
      case 2:
        return '화요일';
      case 3:
        return '수요일';
      case 4:
        return '목요일';
      case 5:
        return '금요일';
      case 6:
        return '토요일';
      case 7:
        return '일요일';
      default:
        return '전체';
    }
  }
}
